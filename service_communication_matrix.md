# Service Communication Matrix

## Overview

This document defines the exact service-to-service communication patterns, which services can call which methods, and the authentication requirements.

## Auth Service RPC Methods

### 🔓 **Public Methods** (No Service Authentication Required)

These methods can be called by any service without client-id/client-key:

| Method        | Description           | Called By                  |
| ------------- | --------------------- | -------------------------- |
| `HealthCheck` | Service health status | Load balancers, monitoring |

### 🔐 **Protected Methods** (Require client-id/client-key)

These methods require valid service credentials:

| Method                          | Description                       | Allowed Services              |
| ------------------------------- | --------------------------------- | ----------------------------- |
| `ValidateUserToken`             | Validate JWT tokens               | coupon-service, order-service |

### 🔧 **Service Management Methods** (Admin Only)

These methods require admin service credentials:

| Method                       | Description                  | Usage                    |
| ---------------------------- | ---------------------------- | ------------------------ |
| `RegisterService`            | Register new service         | Service deployment/setup |
| `ValidateServiceCredentials` | Validate service credentials | Internal validation      |

## User Service RPC Methods ⭐ **UPDATED**

### 🔓 **Public Methods** (No Authentication Required)

| Method        | Description           | Called By                  |
| ------------- | --------------------- | -------------------------- |
| `CreateUser`  | User registration     | API Gateway                |
| `Login`       | User authentication   | API Gateway                |
| `HealthCheck` | Service health status | Load balancers, monitoring |

### 🔐 **Service-to-Service Methods** (Require client-id/client-key)

| Method           | Description                    | Allowed Services |
| ---------------- | ------------------------------ | ---------------- |
| `GetUser`        | Get user by ID                 | auth-service     |
| `GetUserByEmail` | Get user by email (for login)  | auth-service     |

### 🔒 **Protected Methods** (Require JWT Authentication)

| Method | Description      | Called By   |
| ------ | ---------------- | ----------- |
| `GetMe`| Get current user | API Gateway |

## Service Communication Patterns


### 1. **API Gateway → User Service**

```
API Gateway --[Login]--> User Service
```

- **Methods**: `Login`
- **Authentication**: None (public method)
- **Purpose**: User authentication for web/mobile clients

### 2. **Auth Service → User Service** ⭐ **NEW**

```
Auth Service --[GetUserByEmail, GetUser]--> User Service
```

- **Methods**: `GetUserByEmail`, `GetUser`
- **Authentication**: client-id/client-key (auth-service credentials)
- **Purpose**: Email-based user lookup for login (eliminates email duplication)
- **Note**: This replaces direct email storage in auth service

### 4. **Coupon Service → Auth Service**

```
Coupon Service --[ValidateUserToken]--> Auth Service
```

- **Methods**: `ValidateUserToken`
- **Authentication**: client-id/client-key (coupon-service credentials)
- **Purpose**: Token validation

### 4. **Order Service → Auth Service**

```
Order Service --[ValidateUserToken]--> Auth Service
```

- **Methods**: `ValidateUserToken`
- **Authentication**: client-id/client-key (order-service credentials)
- **Purpose**: Token validation for order operations



## Service Credentials Setup

### Required Service Registrations

Each service must be registered with auth-service to obtain credentials:

```bash
# Register user-service
curl -X POST http://auth-service:8080/admin/services \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "service_name": "user-service",
    "service_version": "1.0.0",
    "required_permissions": ["user_management"]
  }'

# Register coupon-service
curl -X POST http://auth-service:8080/admin/services \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "service_name": "coupon-service",
    "service_version": "1.0.0",
    "required_permissions": ["token_validation", "token_revocation"]
  }'

# Register order-service
curl -X POST http://auth-service:8080/admin/services \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "service_name": "order-service",
    "service_version": "1.0.0",
    "required_permissions": ["token_validation"]
  }'
```

### Configuration Example

Each service must configure its credentials:

```yaml
# user-service config
service:
  name: "user-service"
  client_id: "${USER_SERVICE_CLIENT_ID}"
  client_key: "${USER_SERVICE_CLIENT_KEY}"

downstream_services:
  auth_service_addr: "auth-service:50051"
```

## Security Enforcement

### 1. **Method Authorization Matrix**

The auth-service enforces this matrix in code:

```go
var methodAllowList = map[string][]string{
    "coupon-service": {"ValidateUserToken"},
    "order-service":  {"ValidateUserToken"},
}
```

### 2. **Authentication Flow**

1. Service makes gRPC call with metadata:
   ```
   client-id: <service-client-id>
   client-key: <service-client-key>
   ```
2. Auth-service validates credentials against database
3. Auth-service checks method allowlist for the service
4. If authorized, method executes; otherwise returns 403 Forbidden

### 3. **Error Responses**

- **401 Unauthorized**: Missing or invalid credentials
- **403 Forbidden**: Valid credentials but method not allowed for service
- **404 Not Found**: Service not registered

## Implementation Status

### ✅ **Completed**

- [x] Added proper authorization to all protected methods
- [x] Implemented service allowlist enforcement
- [x] **NEW**: Eliminated email duplication between auth and user services
- [x] **NEW**: Added Auth Service → User Service communication
- [x] **NEW**: Implemented `GetUserByEmail` method in user service
- [x] **NEW**: Updated login flow to use user service for email lookup
- [x] **NEW**: Added user type ENUM field to users table
- [x] **NEW**: Implemented cookie-based authentication in API Gateway
- [x] Updated proto definitions with clear method categories
- [x] User-service properly uses client-id/client-key
- [x] Removed `UpdateUserEmail` to eliminate data duplication
- [x] Auth-service fetches user email via `GetUserByEmail`

## API Gateway Authentication Changes ⭐ **NEW**

### Cookie-Based Authentication

The API Gateway now uses HTTP-only cookies instead of returning JWT tokens in response bodies:

#### Login/Register Response Format
```json
{
  "id": "user-uuid",
  "email": "<EMAIL>",
  "name": "User Name",
  "roles": ["USER"],
  "type": "NEW"  // NEW or VIP
}
```

#### Cookie Configuration
- **Access Token Cookie**: `access_token` (24 hours, HTTP-only, Secure, SameSite=Strict)

#### Authentication Middleware
- Automatically validates cookies on protected endpoints
- Clears cookies on logout

### User Type Implementation

#### Database Schema
```sql
-- New ENUM type for user categorization
CREATE TYPE user_type_enum AS ENUM ('NEW', 'VIP');

-- Added to users table
ALTER TABLE users ADD COLUMN type user_type_enum NOT NULL DEFAULT 'NEW';
```

#### API Integration
- User type included in all user-related API responses
- Default value: 'NEW' for new registrations
- Can be updated through admin interfaces (future enhancement)

### 🔄 **Next Steps**

- [ ] Register coupon-service and order-service (if they exist)
- [ ] Add admin endpoint for service registration
- [ ] Implement service credential rotation
- [ ] Add monitoring for unauthorized access attempts

## Testing Service Communication

### Test Valid Service Call


### Test Unauthorized Call


This matrix ensures clear service boundaries and proper authentication for all inter-service communication.
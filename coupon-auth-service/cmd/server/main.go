package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/labstack/echo/v4"
	"google.golang.org/grpc/metadata"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/clients"
	grpc_handler "gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/handler/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/seeds"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/service"
	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/health"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/tracing"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("failed to load config: %v", err))
	}

	logger := logging.New(cfg.Logging.Level, cfg.Logging.Format)
	logger.Infof("Starting service: %s v%s", cfg.Service.Name, cfg.Service.Version)

	tracer, err := tracing.New(cfg.Service.Name, cfg.Jaeger.Host, cfg.Jaeger.Port)
	if err != nil {
		logger.Fatalf("failed to initialize tracer: %v", err)
	}

	appMetrics := metrics.New(cfg.Service.Name)
	jwtManager := auth.NewJWTManager(&cfg.Auth)

	db, err := database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		logger.Fatalf("failed to connect to database: %v", err)
	}

	// Run GORM AutoMigrate
	autoMigrator := database.NewAutoMigrator(db, logger)
	models := []interface{}{
		&model.UserCredential{},
		&model.ServiceCredential{},
		&model.RefreshToken{},
		&model.Role{},
		&model.UserRole{},
	}

	if err := autoMigrator.AutoMigrateWithSeeds(models, seeds.SeedDefaultRoles); err != nil {
		logger.Fatalf("failed to run database migrations: %v", err)
	}
	redisClient := redis.NewClient(&cfg.Redis, logger, appMetrics)

	// Create user service client
	userClient, err := clients.NewUserClient(
		cfg.DownstreamServices.UserServiceAddr,
		&cfg.GRPC,
		logger,
		appMetrics,
		cfg.Service.ClientID,
		cfg.Service.ClientKey,
	)
	if err != nil {
		logger.Fatalf("Failed to create user service client: %v", err)
	}
	defer userClient.Close()

	repo := repository.NewAuthRepository(db, redisClient)
	svc := service.NewAuthService(repo, userClient, jwtManager, logger, cfg)

	healthChecker := health.NewHealthChecker()
	healthChecker.AddCheck("database", db.Health)
	healthChecker.AddCheck("redis", redisClient.Health)

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	var wg sync.WaitGroup
	wg.Add(2)

	go func() {
		defer wg.Done()
		// Create custom auth function that handles both JWT and Bootstrap Token
		authFunc := createAuthFunc(jwtManager, cfg)
		startGRPCServer(ctx, cfg, logger, appMetrics, svc, authFunc)
	}()
	go func() {
		defer wg.Done()
		startHTTPServer(ctx, cfg, logger, healthChecker, appMetrics)
	}()

	wg.Wait()
	tracer.Close()
	db.Close()
	redisClient.Close()
	logger.Info("Shutdown complete")
}

func startGRPCServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, metrics *metrics.Metrics, svc service.AuthService, authFunc func(context.Context) (context.Context, error)) {
	grpcServerWrapper := shared_grpc.NewServer(&cfg.GRPC, logger, metrics, authFunc)
	grpcHandler := grpc_handler.NewAuthServer(svc)
	proto_auth_v1.RegisterAuthServiceServer(grpcServerWrapper.Server, grpcHandler)

	go func() {
		if err := grpcServerWrapper.Start(); err != nil {
			logger.Errorf("gRPC server failed: %v", err)
		}
	}()

	<-ctx.Done()
	grpcServerWrapper.Stop()
}

// createAuthFunc creates a custom auth function that handles JWT tokens, Bootstrap Token, and service credentials
// The strategy is to let most requests pass through to the service layer where proper authorization is handled
func createAuthFunc(jwtManager *auth.JWTManager, cfg *config.Config) func(context.Context) (context.Context, error) {
	return func(ctx context.Context) (context.Context, error) {
		// Get metadata from context
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			// No metadata - let it pass, service layer will handle authorization
			return ctx, nil
		}

		// Check for Bootstrap Token first (for RegisterService method)
		bootstrapTokens := md.Get("bootstrap-token")
		if len(bootstrapTokens) > 0 && bootstrapTokens[0] == cfg.Auth.BootstrapToken {
			// Valid Bootstrap Token, let it pass
			return ctx, nil
		}

		// Check for service credentials (service-to-service calls)
		clientIDs := md.Get("client-id")
		clientKeys := md.Get("client-key")
		if len(clientIDs) > 0 && len(clientKeys) > 0 {
			// This is a service-to-service call, let it pass
			// The actual validation will be done by the service layer
			return ctx, nil
		}

		// Check for JWT token in Authorization header
		authHeaders := md.Get("authorization")
		if len(authHeaders) > 0 {
			// Has authorization header, validate JWT
			return jwtManager.AuthFunc(ctx)
		}

		// No credentials provided - let it pass to service layer
		// Public methods like Login don't require authentication at gRPC level
		return ctx, nil
	}
}

func startHTTPServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, healthChecker *health.HealthChecker, metrics *metrics.Metrics) {
	e := echo.New()
	e.GET("/health", healthChecker.HTTPHandler())
	e.GET(cfg.Metrics.Path, echo.WrapHandler(metrics.Handler()))

	addr := fmt.Sprintf(":%d", cfg.Service.Port)
	server := &http.Server{Addr: addr, Handler: e}

	logger.Infof("Starting operational HTTP server on %s", addr)

	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("HTTP server failed: %v", err)
		}
	}()

	<-ctx.Done()

	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Fatalf("HTTP server shutdown failed: %v", err)
	}
}

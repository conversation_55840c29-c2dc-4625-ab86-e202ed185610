services:
  postgres-auth:
    image: postgres:16-alpine
    container_name: postgres-auth
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-coupon}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-coupon}
      POSTGRES_DB: ${POSTGRES_DB:-auth_db}
    ports:
      - "5433:5432"
    volumes:
      - postgres-auth-data:/var/lib/postgresql/data
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "pg_isready -U ${POSTGRES_USER:-coupon} -d ${POSTGRES_DB:-auth_db}",
        ]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - coupon-network

  redis-auth:
    image: redis:7-alpine
    container_name: redis-auth
    restart: unless-stopped
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD:-}"]
    ports:
      - "6380:6379"
    volumes:
      - redis-auth-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - coupon-network

  jaeger:
    image: jaegertracing/all-in-one:1.56
    container_name: jaeger
    restart: unless-stopped
    ports:
      - "16686:16686"
      - "6831:6831/udp"
    networks:
      - coupon-network

  auth-service:
    build:
      context: .
      args:
        GITLAB_USER: ${GITLAB_USER}
        GITLAB_TOKEN: ${GITLAB_TOKEN}
    container_name: auth-service
    image: registry-gitlab.zalopay.vn/phunn4/coupon-auth-service
    depends_on:
      postgres-auth:
        condition: service_healthy
      redis-auth:
        condition: service_healthy
      jaeger:
        condition: service_started
    env_file:
      - .env
    ports:
      - "8081:8080"
      - "50052:50051"
      - "2113:2112"
    restart: unless-stopped
    networks:
      - coupon-network

  # Example: How a service would be configured with CI/CD-generated credentials
  # This demonstrates how the generated client_id and client_key would be used
  #
  # example-user-service:
  #   build:
  #     context: ../coupon-user-service
  #     args:
  #       GITLAB_USER: ${GITLAB_USER}
  #       GITLAB_TOKEN: ${GITLAB_TOKEN}
  #   container_name: example-user-service
  #   depends_on:
  #     auth-service:
  #       condition: service_started
  #   environment:
  #     # These would be set by CI/CD pipeline after running 'make register-service'
  #     - SERVICE_CLIENT_ID=${USER_SERVICE_CLIENT_ID}  # Generated by registration
  #     - SERVICE_CLIENT_KEY=${USER_SERVICE_CLIENT_KEY}  # Generated by registration
  #     - AUTH_SERVICE_ADDR=auth-service:50051
  #   env_file:
  #     - .env
  #   ports:
  #     - "8082:8080"
  #     - "50053:50051"
  #   restart: unless-stopped
  #   networks:
  #     - coupon-network

volumes:
  postgres-auth-data:
  redis-auth-data:

networks:
  coupon-network:
    name: coupon-network
    driver: bridge
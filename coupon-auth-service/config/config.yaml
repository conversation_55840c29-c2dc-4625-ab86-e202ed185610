service:
  name: "auth-service"
  version: "1.0.0"
  environment: "development"
  port: 8080
  grpc_port: 50051
  client_id: "${AUTH_SERVICE_CLIENT_ID}"
  client_key: "${AUTH_SERVICE_CLIENT_KEY}"

database:
  host: "postgres-auth"
  port: 5432
  user: "${POSTGRES_USER}"
  password: "${POSTGRES_PASSWORD}"
  name: "auth_db"
  ssl_mode: "disable"
  max_open_conns: 10
  max_idle_conns: 5
  max_lifetime: "1h"

redis:
  host: "redis-auth"
  port: 6379
  password: "${REDIS_PASSWORD}"
  db: 0
  pool_size: 10
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"

auth:
  jwt_secret: "${JWT_SECRET_KEY}"
  jwt_expiration: "1h"
  bootstrap_token: "${BOOTSTRAP_TOKEN}"

downstream_services:
  user_service_addr: "user-service:50051"

jaeger:
  host: "jaeger"
  port: 6831

logging:
  level: "debug"
  format: "json"

metrics:
  port: 2112
  path: "/metrics"

grpc:
  host: "0.0.0.0"
  port: 50051
  max_receive_size: 4194304
  max_send_size: 4194304
  connection_timeout: "5s"
  keepalive_time: "30s"
  keepalive_timeout: "5s"
  max_connection_idle: "90s"

package repository

import (
	"context"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

type authInfrastructure struct {
	db    *database.DB
	redis *redis.Client
}

type AuthRepository interface {
	RegisterService(ctx context.Context, service *model.ServiceCredential) error
	GetServiceByClientID(ctx context.Context, clientID string) (*model.ServiceCredential, error)
}

func NewAuthRepository(db *database.DB, redis *redis.Client) AuthRepository {
	return &authInfrastructure{db: db, redis: redis}
}

func (r *authInfrastructure) RegisterService(ctx context.Context, service *model.ServiceCredential) error {
	return r.db.WithContext(ctx).Create(service).Error
}

func (r *authInfrastructure) GetServiceByClientID(ctx context.Context, clientID string) (*model.ServiceCredential, error) {
	var cred model.ServiceCredential
	if err := r.db.WithContext(ctx).Where("client_id = ?", clientID).First(&cred).Error; err != nil {
		return nil, err
	}
	return &cred, nil
}

package middleware

import (
	"context"
	"path"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"golang.org/x/crypto/bcrypt"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

var publicMethods = map[string]bool{
       "HealthCheck": true,
}

var methodAllowList = map[string][]string{
	"coupon-service": {"ValidateUserToken"},
	"order-service":  {"ValidateUserToken"},
	"api-gateway":    {},
}

func CreateServiceAuthFunc(repo repository.AuthRepository, cfg *config.Config) func(context.Context) (context.Context, error) {
	return func(ctx context.Context) (context.Context, error) {
		ts := grpc.ServerTransportStreamFromContext(ctx)
		method := ""
		if ts != nil {
			method = path.Base(ts.Method())
		}
		if publicMethods[method] {
			return ctx, nil
		}

		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			return nil, status.Error(codes.Unauthenticated, "missing metadata")
		}

		if method == "RegisterService" {
			tokens := md.Get("bootstrap-token")
			if len(tokens) > 0 && tokens[0] == cfg.Auth.BootstrapToken {
				return ctx, nil
			}
		}

		ids := md.Get("client-id")
		keys := md.Get("client-key")
		if len(ids) == 0 || len(keys) == 0 {
			return nil, status.Error(codes.Unauthenticated, "missing service credentials")
		}

		cred, err := repo.GetServiceByClientID(ctx, ids[0])
		if err != nil {
			return nil, status.Error(codes.Unauthenticated, "invalid service credentials")
		}
		if bcrypt.CompareHashAndPassword([]byte(cred.ClientKey), []byte(keys[0])) != nil {
			return nil, status.Error(codes.Unauthenticated, "invalid service credentials")
		}

		allowed := methodAllowList[cred.Name]
		allowedMethod := false
		for _, m := range allowed {
			if m == method {
				allowedMethod = true
				break
			}
		}
		if !allowedMethod {
			return nil, status.Errorf(codes.PermissionDenied, "method %s not allowed for service %s", method, cred.Name)
		}

		return ctx, nil
	}
}

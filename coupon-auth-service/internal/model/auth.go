package model

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

type ServiceCredential struct {
	ID        string    `gorm:"type:uuid;primary_key;"`
	Name      string    `gorm:"type:varchar(255);not null;unique"`
	ClientID  string    `gorm:"type:varchar(255);not null;unique"`
	ClientKey string    `gorm:"not null"`
	Version   string    `gorm:"type:varchar(50)"`
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (ServiceCredential) TableName() string { return "service_credentials" }

func NewServiceCredential(name, version string) (*ServiceCredential, string, error) {
	rawKey, err := generateRandomKey(32)
	if err != nil {
		return nil, "", err
	}

	keyHash, err := hashKey(rawKey)
	if err != nil {
		return nil, "", err
	}

	return &ServiceCredential{
		ID:        uuid.NewString(),
		Name:      name,
		Version:   version,
		ClientID:  uuid.NewString(),
		ClientKey: keyHash,
	}, rawKey, nil
}

// Role represents predefined user roles.
type Role string

const (
	RoleUser  Role = "USER"
	RoleAdmin Role = "ADMIN"
)

func generateRandomKey(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func hashKey(key string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(key), bcrypt.DefaultCost)
	return string(bytes), err
}

package service

import (
	"context"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/repository"
       proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
       proto_user_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
       "gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
       "gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
       "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
       "gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
       "golang.org/x/crypto/bcrypt"
)

type AuthService interface {
       RegisterService(ctx context.Context, req *proto_auth_v1.RegisterServiceRequest) (*proto_auth_v1.RegisterServiceResponse, error)
       ValidateServiceCredentials(ctx context.Context, req *proto_auth_v1.ValidateServiceCredentialsRequest) (*proto_auth_v1.ValidateServiceCredentialsResponse, error)
       ValidateUserToken(ctx context.Context, req *proto_auth_v1.ValidateUserTokenRequest) (*proto_auth_v1.ValidateUserTokenResponse, error)
}

type authService struct {
	repo       repository.AuthRepository
	userClient UserClient
	jwtManager *auth.JWTManager
	logger     *logging.Logger
	cfg        *config.Config
}

// UserClient interface for dependency injection
type UserClient interface {
	GetUserByEmail(ctx context.Context, email string) (*proto_user_v1.User, error)
	GetUserByID(ctx context.Context, userID string) (*proto_user_v1.User, error)
}

func NewAuthService(repo repository.AuthRepository, userClient UserClient, jwtManager *auth.JWTManager, logger *logging.Logger, cfg *config.Config) AuthService {
	return &authService{
		repo:       repo,
		userClient: userClient,
		jwtManager: jwtManager,
		logger:     logger,
		cfg:        cfg,
	}
}


// UpdateUserEmail is no longer needed since auth service doesn't store email
// Email updates are handled entirely by the user service

func (s *authService) ValidateUserToken(ctx context.Context, req *proto_auth_v1.ValidateUserTokenRequest) (*proto_auth_v1.ValidateUserTokenResponse, error) {
	claims, err := s.jwtManager.ValidateToken(req.Token)
	if err != nil {
		return nil, errors.NewUnauthorizedError("invalid token")
	}

	if req.RequiredRole != "" {
		hasRole := false
		for _, r := range claims.Roles {
			if r == req.RequiredRole {
				hasRole = true
				break
			}
		}
		if !hasRole {
			return nil, errors.NewForbiddenError("user does not have the required role")
		}
	}

	return &proto_auth_v1.ValidateUserTokenResponse{
		Valid:  true,
		UserId: claims.UserID,
		Email:  claims.Email,
		Roles:  claims.Roles,
	}, nil
}

func (s *authService) RegisterService(ctx context.Context, req *proto_auth_v1.RegisterServiceRequest) (*proto_auth_v1.RegisterServiceResponse, error) {
	cred, rawKey, err := model.NewServiceCredential(req.ServiceName, req.ServiceVersion)
	if err != nil {
		return nil, errors.NewInternalError("could not generate credentials")
	}

	if err := s.repo.RegisterService(ctx, cred); err != nil {
		return nil, errors.NewInternalError("failed to save service credentials")
	}

	return &proto_auth_v1.RegisterServiceResponse{
		ServiceId: cred.ID,
		ClientId:  cred.ClientID,
		ClientKey: rawKey,
	}, nil
}

func (s *authService) ValidateServiceCredentials(ctx context.Context, req *proto_auth_v1.ValidateServiceCredentialsRequest) (*proto_auth_v1.ValidateServiceCredentialsResponse, error) {
	cred, err := s.repo.GetServiceByClientID(ctx, req.ClientId)
	if err != nil {
		return nil, errors.NewUnauthorizedError("invalid client id or key")
	}

	if bcrypt.CompareHashAndPassword([]byte(cred.ClientKey), []byte(req.ClientKey)) != nil {
		return nil, errors.NewUnauthorizedError("invalid client id or key")
	}

	return &proto_auth_v1.ValidateServiceCredentialsResponse{
		Valid:       true,
		ServiceId:   cred.ID,
		ServiceName: cred.Name,
	}, nil
}

# Coupon Auth Service

This service provides authentication and authorization functionality for other coupon microservices. It exposes a gRPC API and a small HTTP server for health checks and metrics.

## Requirements

- Go 1.24+
- Docker (for containerised development)

## Configuration

Configuration is loaded from `config/config.yaml` and environment variables. A sample `.env.example` file is provided. Copy it to `.env` and adjust the values if you plan to run locally or with Docker Compose.

## Makefile Targets

### Development Targets

- `build` - Build the auth server binary
- `build-cli` - Build the CLI tool for service registration
- `run` - Build and run the auth server
- `test` - Run all tests
- `docker-build` - Build Docker image
- `compose-up` - Start services with Docker Compose
- `compose-down` - Stop Docker Compose services

### CI/CD Service Registration

- `register-service` - Register a service with the auth service (simulates CI/CD pipeline)

**Usage:**
```bash
make register-service SERVICE_NAME=user-service SERVICE_VERSION=1.0.0 DESCRIPTION="User management service"
```

This command:
1. Builds the CLI tool
2. Connects to the auth service using Bootstrap Token
3. Registers the service and generates unique credentials
4. Outputs credentials in CI/CD-friendly format
5. Saves credentials to a file for easy consumption

**Example Output:**
```
✅ Service registration successful!
Service ID: 123e4567-e89b-12d3-a456-426614174000
Client ID: 456e7890-e89b-12d3-a456-426614174001
Client Key: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6

# Environment variables for CI/CD:
export USER_SERVICE_CLIENT_ID=456e7890-e89b-12d3-a456-426614174001
export USER_SERVICE_CLIENT_KEY=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6

📄 Credentials saved to: user-service-credentials.env
```

For detailed documentation, see [CI_CD_SERVICE_REGISTRATION.md](./CI_CD_SERVICE_REGISTRATION.md).

- `make build` – build the Go binary
- `make run` – run the service directly
- `make test` – run Go tests
- `make docker-build` – build the Docker image
- `make compose-up` – start the service and dependencies using Docker Compose
- `make compose-down` – stop the Docker Compose services

## Running with Docker Compose

1. Copy `.env.example` to `.env` and fill in the required values.
2. Run `make compose-up` to build and start the service along with Postgres, Redis and Jaeger.
3. The service will be available on `localhost:8080` for HTTP, `localhost:50051` for gRPC and `localhost:2112` for metrics.

Use `make compose-down` to stop the containers.

## Testing

Run `make test` to execute unit tests. Note that downloading dependencies may require internet access.

BINARY=auth-server
CLI_BINARY=auth-cli

.PHONY: build run test docker-build compose-up compose-down build-cli register-service

build:
	go build -o $(BINARY) ./cmd/server

build-cli:
	go build -o $(CLI_BINARY) ./cmd/cli

run: build
	./$(BINARY)

# CI/CD Service Registration Simulation
# Usage: make register-service SERVICE_NAME=user-service SERVICE_VERSION=1.0.0 [DESCRIPTION="User management service"]
register-service: build-cli
	@if [ -z "$(SERVICE_NAME)" ]; then \
		echo "Error: SERVICE_NAME is required"; \
		echo "Usage: make register-service SERVICE_NAME=user-service [SERVICE_VERSION=1.0.0] [DESCRIPTION='Service description']"; \
		exit 1; \
	fi
	@echo "🚀 Simulating CI/CD Service Registration for $(SERVICE_NAME)"
	@echo "=================================================="
	./$(CLI_BINARY) \
		-service-name="$(SERVICE_NAME)" \
		-service-version="$(if $(SERVICE_VERSION),$(SERVICE_VERSION),1.0.0)" \
		-description="$(if $(DESCRIPTION),$(DESCRIPTION),Microservice: $(SERVICE_NAME))" \
		-auth-addr="localhost:50052"

test:
	go test ./...

docker-build:
	docker build -t coupon-auth-service .

compose-up:
	docker-compose up -d --build

compose-down:
	docker-compose down

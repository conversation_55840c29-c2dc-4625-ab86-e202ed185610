package clients

import (
	"context"
	"fmt"
	"time"

	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

type AuthClient struct {
	Client    proto_auth_v1.AuthServiceClient
	conn      *shared_grpc.Client
	clientID  string
	clientKey string
}

func NewAuthClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*AuthClient, error) {
	client, err := shared_grpc.NewClient(target, cfg, logger, metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for auth service: %w", err)
	}

	return &AuthClient{
		Client:    proto_auth_v1.NewAuthServiceClient(client.Conn),
		conn:      client,
		clientID:  clientID,
		clientKey: clientKey,
	}, nil
}

func (c *AuthClient) Close() {
	c.conn.Close()
}

func (c *AuthClient) Login(ctx context.Context, email, password string) (string, error) {
	req := &proto_auth_v1.LoginRequest{
		Email:    email,
		Password: password,
	}

	// Add service credentials to metadata
	md := metadata.New(map[string]string{
		"client-id":  c.clientID,
		"client-key": c.clientKey,
	})
	ctx = metadata.NewOutgoingContext(ctx, md)

	// Add timeout
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	var header metadata.MD
	_, err := c.Client.Login(ctx, req, grpc.Header(&header))
	if err != nil {
		return "", err
	}
	tokens := header.Get("access_token")
	if len(tokens) == 0 {
		return "", fmt.Errorf("missing access token in response")
	}
	return tokens[0], nil
}

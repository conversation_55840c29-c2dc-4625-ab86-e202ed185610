package clients

import (
	"context"
	"fmt"
	"time"

	user_proto_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
       shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
       "gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
       "gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
       "google.golang.org/grpc"
       "google.golang.org/grpc/metadata"
)

type UserClient struct {
	Client    user_proto_v1.UserServiceClient
	conn      *shared_grpc.Client
	clientID  string
	clientKey string
}

func NewUserClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*UserClient, error) {
	client, err := shared_grpc.NewClient(target, cfg, logger, metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for user service: %w", err)
	}
	return &UserClient{
		Client:    user_proto_v1.NewUserServiceClient(client.Conn),
		conn:      client,
		clientID:  clientID,
		clientKey: clientKey,
	}, nil
}

func (c *UserClient) Close() {
	c.conn.Close()
}

func (c *UserClient) CreateUser(ctx context.Context, name, email, password string) (*user_proto_v1.CreateUserResponse, error) {
	req := &user_proto_v1.CreateUserRequest{
		Name:     name,
		Email:    email,
		Password: password,
	}

	// Add service credentials to metadata
	md := metadata.New(map[string]string{
		"client-id":  c.clientID,
		"client-key": c.clientKey,
	})
	ctx = metadata.NewOutgoingContext(ctx, md)

	// Add timeout
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.CreateUser(ctx, req)
}

func (c *UserClient) GetUser(ctx context.Context, userID string) (*user_proto_v1.GetUserResponse, error) {
	req := &user_proto_v1.GetUserRequest{UserId: userID}

	// Add service credentials to metadata
	md := metadata.New(map[string]string{
		"client-id":  c.clientID,
		"client-key": c.clientKey,
	})
	ctx = metadata.NewOutgoingContext(ctx, md)

	// Add timeout
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.GetUser(ctx, req)
}

func (c *UserClient) GetUserByEmail(ctx context.Context, email string) (*user_proto_v1.GetUserByEmailResponse, error) {
	req := &user_proto_v1.GetUserByEmailRequest{Email: email}

	// Add service credentials to metadata
	md := metadata.New(map[string]string{
		"client-id":  c.clientID,
		"client-key": c.clientKey,
	})
	ctx = metadata.NewOutgoingContext(ctx, md)

	// Add timeout
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

        return c.Client.GetUserByEmail(ctx, req)
}

func (c *UserClient) Login(ctx context.Context, email, password string) (*user_proto_v1.LoginResponse, metadata.MD, error) {
       req := &user_proto_v1.LoginRequest{Email: email, Password: password}

       md := metadata.New(map[string]string{
               "client-id":  c.clientID,
               "client-key": c.clientKey,
       })
       ctx = metadata.NewOutgoingContext(ctx, md)

       ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
       defer cancel()

       var header metadata.MD
       resp, err := c.Client.Login(ctx, req, grpc.Header(&header))
       if err != nil {
               return nil, nil, err
       }
       return resp, header, nil
}

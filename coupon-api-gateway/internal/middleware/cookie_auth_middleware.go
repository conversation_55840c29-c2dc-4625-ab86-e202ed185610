package middleware

import (
       "net/http"
       "strings"

       "github.com/labstack/echo/v4"
       "gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
       "gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
       "gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

// CookieAuthConfig holds configuration for cookie-based authentication
type CookieAuthConfig struct {
       JWTManager   *auth.JWTManager
       CookieConfig *utils.CookieConfig
       Logger       *logging.Logger
       SkipPaths    []string // Paths that don't require authentication
}

// CookieAuth creates a middleware for cookie-based authentication
func CookieAuth(config *CookieAuthConfig) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Skip authentication for certain paths
			if shouldSkipAuth(c.Request().URL.Path, config.SkipPaths) {
				return next(c)
			}

			// Try to get access token from cookie
			accessToken, err := utils.GetAccessTokenFromCookie(c)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
			}

			// Validate the access token
			claims, err := config.JWTManager.ValidateToken(accessToken)
			if err != nil {
				config.Logger.WithContext(c.Request().Context()).
					Debugf("Access token validation failed: %v", err)
				utils.ClearAuthCookies(c, config.CookieConfig)
				return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
			}

			// Token is valid, set user context
			c.Set("userID", claims.UserID)
			c.Set("userEmail", claims.Email)
			c.Set("userRoles", claims.Roles)

			return next(c)
		}
	}
}

// shouldSkipAuth checks if authentication should be skipped for the given path
func shouldSkipAuth(path string, skipPaths []string) bool {
	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}

// RequireAuth is a stricter middleware that always requires authentication
func RequireAuth(config *CookieAuthConfig) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Try to get access token from cookie
			accessToken, err := utils.GetAccessTokenFromCookie(c)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, "Access token required")
			}

			// Validate the access token
			claims, err := config.JWTManager.ValidateToken(accessToken)
			if err != nil {
				utils.ClearAuthCookies(c, config.CookieConfig)
				return echo.NewHTTPError(http.StatusUnauthorized, "Invalid access token")
			}

			// Token is valid, set user context
			c.Set("userID", claims.UserID)
			c.Set("userEmail", claims.Email)
			c.Set("userRoles", claims.Roles)

			return next(c)
		}
	}
}

// RequireRole creates a middleware that requires specific roles
func RequireRole(roles ...string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userRoles, ok := c.Get("userRoles").([]string)
			if !ok {
				return echo.NewHTTPError(http.StatusForbidden, "User roles not found")
			}

			// Check if user has any of the required roles
			for _, requiredRole := range roles {
				for _, userRole := range userRoles {
					if userRole == requiredRole {
						return next(c)
					}
				}
			}

			return echo.NewHTTPError(http.StatusForbidden, "Insufficient permissions")
		}
	}
}

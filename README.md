# Coupon Microservices

This repository contains multiple Go services that together form a coupon management platform. Each service can run independently using Docker Compose. This guide explains how to set up the entire system for local development.

## Repository Structure

- **coupon-api-gateway** – HTTP gateway that exposes public endpoints.
- **coupon-auth-service** – gRPC authentication service.
- **coupon-user-service** – Handles user management and publishes Kafka events.
- **coupon-proto** – Protobuf definitions and generated Go code.
- **coupon-shared-libs** – Common Go packages used by the services.

## Prerequisites

- [Go 1.24+](https://go.dev/)
- [Docker](https://www.docker.com/)
- [Docker Compose](https://docs.docker.com/compose/)
- `make` utility (optional, but used by the provided Makefiles)

## 1. Clone the Repository

```bash
git clone <repo-url>
cd coupon-microservices
```

## 2. Generate Protobuf Code

The API contracts are stored in `coupon-proto`. Before running the services you should generate the Go stubs:

```bash
cd coupon-proto
make install-deps    # installs buf and protoc plugins
make generate        # generates Go code under gen/
cd ..
```

## 3. Prepare Environment Files

Each service has an `.env.example` file. Copy it to `.env` and edit the values if needed:

```bash
cp coupon-auth-service/.env.example coupon-auth-service/.env
cp coupon-user-service/.env.example coupon-user-service/.env
cp coupon-api-gateway/.env.example coupon-api-gateway/.env
```

The default values expose the services on `localhost` with common ports.

## 4. Start the Services

Open a terminal for each service and run `make compose-up`. Docker Compose will start the service together with its dependencies (databases, Redis, Jaeger, Kafka, etc.).

```bash
# Terminal 1 – Auth Service
cd coupon-auth-service
make compose-up

# Terminal 2 – User Service
cd ../coupon-user-service
make compose-up

# Terminal 3 – API Gateway
cd ../coupon-api-gateway
make compose-up
```

The services expose the following ports by default:

- **API Gateway** – `http://localhost:8080`
- **Auth Service** – HTTP `localhost:8080`, gRPC `localhost:50051`
- **User Service** – HTTP `localhost:8080`, gRPC `localhost:50051`

Prometheus metrics are available on port `2112` for each service and Jaeger UI on `http://localhost:16686`.

To stop a service stack, run `make compose-down` inside that service directory.

## 5. Additional Documentation

- Each service directory contains a dedicated `README.md` with more details about configuration and available Makefile targets.
- The [service_communication_matrix.md](service_communication_matrix.md) file documents how services communicate and which RPC methods are exposed.

## Troubleshooting

Port conflicts can occur if multiple services try to expose the same database or gRPC ports. You may modify the exposed ports in each `docker-compose.yml` if needed.

## License

This project is provided for educational purposes. See individual service directories for license information.
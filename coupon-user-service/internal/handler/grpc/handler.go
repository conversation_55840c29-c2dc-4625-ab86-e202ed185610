package grpc_handler

import (
	"context"

       app_errors "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"

       commonv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
       userv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"

       "gitlab.zalopay.vn/phunn4/coupon-user-service/internal/model"
       "gitlab.zalopay.vn/phunn4/coupon-user-service/internal/service"
       "google.golang.org/grpc"
       "google.golang.org/grpc/metadata"
       "google.golang.org/protobuf/types/known/timestamppb"
)

type UserServer struct {
	userv1.UnimplementedUserServiceServer
	svc service.UserService
}

func NewUserServer(svc service.UserService) *UserServer {
	return &UserServer{svc: svc}
}

// convertUserTypeToProto converts model.UserType to proto UserType
func convertUserTypeToProto(userType model.UserType) userv1.UserType {
	switch userType {
	case model.UserTypeNew:
		return userv1.UserType_USER_TYPE_NEW
	case model.UserTypeVIP:
		return userv1.UserType_USER_TYPE_VIP
	default:
		return userv1.UserType_USER_TYPE_UNSPECIFIED
	}
}

func (s *UserServer) Login(ctx context.Context, req *userv1.LoginRequest) (*userv1.LoginResponse, error) {
       user, token, err := s.svc.Login(ctx, req.Email, req.Password)
       if err != nil {
               return nil, app_errors.ToGRPCError(err)
       }

       grpc.SetHeader(ctx, metadata.Pairs("access_token", token))

       roles := []string{string(user.Role)}

       return &userv1.LoginResponse{
               User: &userv1.User{
                       Id:           user.ID,
                       Email:        user.Email,
                       Name:         user.Name,
                       Roles:        roles,
                       Type:         convertUserTypeToProto(user.Type),
                       CreatedAt:    timestamppb.New(user.CreatedAt),
                       PasswordHash: user.PasswordHash,
                       UpdatedAt:    timestamppb.New(user.UpdatedAt),
               },
       }, nil
}

func (s *UserServer) GetUser(ctx context.Context, req *userv1.GetUserRequest) (*userv1.GetUserResponse, error) {
	user, err := s.svc.GetUser(ctx, req.UserId)
	if err != nil {
		return nil, app_errors.ToGRPCError(err)
	}

	roles := []string{string(user.Role)}

	return &userv1.GetUserResponse{
		User: &userv1.User{
			Id:           user.ID,
			Email:        user.Email,
			Name:         user.Name,
			Roles:        roles,
			Type:         convertUserTypeToProto(user.Type),
			CreatedAt:    timestamppb.New(user.CreatedAt),
			PasswordHash: user.PasswordHash,
			UpdatedAt:    timestamppb.New(user.UpdatedAt),
		},
	}, nil
}

func (s *UserServer) GetUserByEmail(ctx context.Context, req *userv1.GetUserByEmailRequest) (*userv1.GetUserByEmailResponse, error) {
	user, err := s.svc.GetUserByEmail(ctx, req.Email)
	if err != nil {
		return nil, app_errors.ToGRPCError(err)
	}

	roles := []string{string(user.Role)}

	return &userv1.GetUserByEmailResponse{
		User: &userv1.User{
			Id:           user.ID,
			Email:        user.Email,
			Name:         user.Name,
			Roles:        roles,
			Type:         convertUserTypeToProto(user.Type),
			CreatedAt:    timestamppb.New(user.CreatedAt),
			PasswordHash: user.PasswordHash,
			UpdatedAt:    timestamppb.New(user.UpdatedAt),
		},
	}, nil
}

func (s *UserServer) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserResponse, error) {
	user, err := s.svc.RegisterUser(ctx, req.Name, req.Email, req.Password)
	if err != nil {
		return nil, app_errors.ToGRPCError(err)
	}

	roles := []string{string(user.Role)}

	return &userv1.CreateUserResponse{
		User: &userv1.User{
			Id:           user.ID,
			Email:        user.Email,
			Name:         user.Name,
			Roles:        roles,
			Type:         convertUserTypeToProto(user.Type),
			CreatedAt:    timestamppb.New(user.CreatedAt),
			PasswordHash: user.PasswordHash,
			UpdatedAt:    timestamppb.New(user.UpdatedAt),
		},
	}, nil
}

func (s *UserServer) HealthCheck(ctx context.Context, req *commonv1.HealthCheckRequest) (*commonv1.HealthCheckResponse, error) {
	return &commonv1.HealthCheckResponse{Status: commonv1.HealthCheckResponse_SERVING_STATUS_SERVING}, nil
}

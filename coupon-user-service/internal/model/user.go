package model

import (
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

// UserType represents the type of user
type UserType string

const (
	UserTypeNew UserType = "NEW"
	UserTypeVIP UserType = "VIP"
)

// User represents user profile data only
// Roles and authentication data are managed by auth-service
// Role defines a user's role within the system
type Role string

const (
	RoleUser  Role = "USER"
	RoleAdmin Role = "ADMIN"
)

// User represents user profile data only.
// Authentication data is managed by auth-service
type User struct {
	ID           string    `gorm:"type:uuid;primary_key;"`
	Name         string    `gorm:"type:varchar(255);not null"`
	Email        string    `gorm:"type:varchar(255);not null;uniqueIndex"`
	PasswordHash string    `gorm:"not null"`
	Role         Role      `gorm:"type:varchar(10);not null;default:'USER'"`
	Type         UserType  `gorm:"type:varchar(10);not null;default:'NEW'"`
	CreatedAt    time.Time `gorm:"not null"`
	UpdatedAt    time.Time `gorm:"not null"`
}

func (User) TableName() string { return "users" }

func NewUser(name, email, password string) (*User, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}
	return &User{
		ID:           uuid.NewString(),
		Name:         name,
		Email:        email,
		PasswordHash: string(hash),
		Role:         RoleUser,
		Type:         UserTypeNew, // Default to NEW user type
	}, nil
}

func (u *User) CheckPassword(password string) bool {
	return bcrypt.CompareHashAndPassword([]byte(u.PasswordHash), []byte(password)) == nil
}

package service

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	app_errors "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/repository"
)

type UserService interface {
	RegisterUser(ctx context.Context, name, email, password string) (*model.User, error)
	GetUser(ctx context.Context, userID string) (*model.User, error)
	GetUserByEmail(ctx context.Context, email string) (*model.User, error)
}

type userService struct {
	repo       repository.UserRepository
	authClient *clients.AuthClient
	logger     *logging.Logger
}

func NewUserService(repo repository.UserRepository, authClient *clients.AuthClient, logger *logging.Logger) UserService {
	return &userService{
		repo:       repo,
		authClient: authClient,
		logger:     logger,
	}
}

func (s *userService) RegisterUser(ctx context.Context, name, email, password string) (*model.User, error) {
	log := s.logger.WithContext(ctx)
	log.Infof("Attempting to register user with email: %s", email)

	_, err := s.repo.GetByEmail(ctx, email)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, app_errors.NewInternalError(fmt.Sprintf("db error checking for existing user: %v", err))
	}
	if err == nil {
		return nil, app_errors.NewConflictError(fmt.Sprintf("user with email %s already exists", email))
	}

	user := model.NewUser(name, email)
	if err := s.repo.Create(ctx, user); err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to create user profile: %v", err))
	}
	log.Infof("User profile created for user_id: %s", user.ID)

	// Create credentials and assign default role via auth-service
	// Note: Email is no longer passed to auth service as it's not stored there
	if err := s.authClient.CreateUserCredentialsWithRole(ctx, user.ID, "", password, "USER"); err != nil {
		log.Errorf("CRITICAL: Failed to create credentials for user %s after profile creation: %v", user.ID, err)
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to finalize user registration: %v", err))
	}
	log.Infof("User credentials and role created via Auth service for user_id: %s", user.ID)

	return user, nil
}

func (s *userService) GetUser(ctx context.Context, userID string) (*model.User, error) {
	user, err := s.repo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, app_errors.NewNotFoundError(fmt.Sprintf("user with id %s not found", userID))
		}
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to get user: %v", err))
	}
	return user, nil
}

func (s *userService) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	user, err := s.repo.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, app_errors.NewNotFoundError(fmt.Sprintf("user with email %s not found", email))
		}
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to get user by email: %v", err))
	}
	return user, nil
}

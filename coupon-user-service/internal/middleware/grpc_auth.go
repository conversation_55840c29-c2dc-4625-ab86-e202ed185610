package middleware

import (
	"context"
	"strings"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

// PublicMethods defines which methods don't require authentication
var PublicMethods = map[string]bool{
	"/user.v1.UserService/CreateUser":  true,
	"/user.v1.UserService/HealthCheck": true,
}

// ServiceToServiceMethods defines which methods support service-to-service authentication
var ServiceToServiceMethods = map[string]bool{
	"/user.v1.UserService/GetUserByEmail": true,
	"/user.v1.UserService/GetUser":        true,
}

// CreateSelectiveAuthInterceptor creates a gRPC interceptor that handles authentication
// for public methods, service-to-service calls, and JWT authentication
func CreateSelectiveAuthInterceptor(jwtManager *auth.JWTManager) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Check if this is a public method
		if PublicMethods[info.FullMethod] {
			return handler(ctx, req) // Skip authentication for public methods
		}

		// Get metadata from context
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			// No metadata, apply JWT authentication
			newCtx, err := jwtManager.AuthFunc(ctx)
			if err != nil {
				return nil, err
			}
			return handler(newCtx, req)
		}

		// Check if this is a service-to-service method
		if ServiceToServiceMethods[info.FullMethod] {
			// Check for service credentials first
			clientIDs := md.Get("client-id")
			clientKeys := md.Get("client-key")
			if len(clientIDs) > 0 && len(clientKeys) > 0 {
				// This is a service-to-service call, let it pass
				// The actual validation will be done by the auth service
				return handler(ctx, req)
			}
		}

		// For non-public methods without service credentials, apply JWT authentication
		newCtx, err := jwtManager.AuthFunc(ctx)
		if err != nil {
			return nil, err
		}
		return handler(newCtx, req)
	}
}

// CreateSelectiveAuthFunc creates an auth function that handles authentication
// Logic:
// 1. If service credentials are present, let it pass (service-to-service calls)
// 2. Otherwise, apply JWT authentication
// Note: Public methods like CreateUser are handled by allowing service-to-service calls
func CreateSelectiveAuthFunc(jwtManager *auth.JWTManager) func(context.Context) (context.Context, error) {
	return func(ctx context.Context) (context.Context, error) {
		// Get metadata from context
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			// No metadata, apply JWT authentication
			return jwtManager.AuthFunc(ctx)
		}

		// Check for service credentials first (service-to-service calls)
		clientIDs := md.Get("client-id")
		clientKeys := md.Get("client-key")
		if len(clientIDs) > 0 && len(clientKeys) > 0 {
			// This is a service-to-service call, let it pass
			// The actual validation will be done by the auth service if needed
			return ctx, nil
		}

		// No service credentials, apply JWT authentication
		return jwtManager.AuthFunc(ctx)
	}
}

// IsPublicMethod checks if a method is public
func IsPublicMethod(fullMethod string) bool {
	// Extract just the method part from the full method path
	parts := strings.Split(fullMethod, "/")
	if len(parts) >= 3 {
		method := "/" + parts[1] + "/" + parts[2]
		return PublicMethods[method]
	}
	return PublicMethods[fullMethod]
}

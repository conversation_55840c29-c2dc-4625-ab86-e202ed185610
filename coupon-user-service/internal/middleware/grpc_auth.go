package middleware

import (
	"context"
	"path"
	"slices"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/clients"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

// publicMethods defines which methods don't require authentication
var publicMethods = map[string]bool{
       "CreateUser":  true,
       "Login":       true,
       "HealthCheck": true,
}

// ServiceToServiceMethods defines which methods support service-to-service authentication
var serviceToServiceMethods = map[string]bool{
	"GetUserByEmail": true,
	"GetUser":        true,
}

// methodAllowList maps service names to allowed methods
var methodAllowList = map[string][]string{
	"auth-service": {
		"GetUserByEmail",
		"GetUser",
	},
}

// CreateSelectiveAuthInterceptor creates a gRPC interceptor that handles authentication
// for public methods, service-to-service calls, and JWT authentication
func CreateSelectiveAuthInterceptor(jwtManager *auth.JWTManager) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		method := path.Base(info.FullMethod)
		if publicMethods[method] {
			return handler(ctx, req)
		}

		// Get metadata from context
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			// No metadata, apply JWT authentication
			newCtx, err := jwtManager.AuthFunc(ctx)
			if err != nil {
				return nil, err
			}
			return handler(newCtx, req)
		}

		// Check if this is a service-to-service method
		if serviceToServiceMethods[method] {
			// Check for service credentials first
			clientIDs := md.Get("client-id")
			clientKeys := md.Get("client-key")
			if len(clientIDs) > 0 && len(clientKeys) > 0 {
				// This is a service-to-service call, let it pass
				// The actual validation will be done by the auth service
				return handler(ctx, req)
			}
		}

		// For non-public methods without service credentials, apply JWT authentication
		newCtx, err := jwtManager.AuthFunc(ctx)
		if err != nil {
			return nil, err
		}
		return handler(newCtx, req)
	}
}

// CreateSelectiveAuthFunc creates an auth function that handles authentication
// Logic:
// 1. If service credentials are present, let it pass (service-to-service calls)
// 2. Otherwise, apply JWT authentication
// Note: Public methods like CreateUser are handled by allowing service-to-service calls
func CreateServiceAuthFunc(jwtManager *auth.JWTManager, authClient *clients.AuthClient) func(context.Context) (context.Context, error) {
	return func(ctx context.Context) (context.Context, error) {
		ts := grpc.ServerTransportStreamFromContext(ctx)
		method := ""
		if ts != nil {
			method = path.Base(ts.Method())
		}

		if publicMethods[method] {
			return ctx, nil
		}

		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			return jwtManager.AuthFunc(ctx)
		}

		clientIDs := md.Get("client-id")
		clientKeys := md.Get("client-key")
		if len(clientIDs) > 0 && len(clientKeys) > 0 {
			serviceName, err := authClient.ValidateServiceCredentials(ctx, clientIDs[0], clientKeys[0])
			if err != nil {
				return nil, err
			}
			allowed := methodAllowList[serviceName]
			if !slices.Contains(allowed, method) {
				return nil, status.Errorf(codes.PermissionDenied, "method %s not allowed for service %s", method, serviceName)
			}
			return ctx, nil
		}

		return jwtManager.AuthFunc(ctx)
	}
}

// isPublicMethod checks if a method is public
func isPublicMethod(fullMethod string) bool {
	return publicMethods[path.Base(fullMethod)]
}
